from typing import Any, Dict, <PERSON><PERSON>, Optional
import numpy as np
import logging

from .sphere_projection import visibility_via_sphere_projection

logger = logging.getLogger(__name__)


def compute_visibility(
    box3d: Dict,
    cam,
    depth_img: np.ndarray,
    cfg: Dict,
    method_arg: str,
    samples: int,
    enable_diagnostics: bool,
    debug_dir: Optional[str],
    obj_id: Any,
    cam_name: str,
) -> Tuple[float, Dict]:
    """
    Compute visibility using Sphere Projection (single, production method).

    Notes:
    - The dispatcher is simplified to a single method (sphere) and ignores
      method_arg and samples for compatibility with existing callers.
    - Configuration keys read from cfg['visibility']:
        sphere_radius_m, tau_base_m, tau_scale_per_m,
        occlusion_fraction_thr, min_neighbors,
        treat_no_depth_as_visible, max_window_px
    """
    vis_cfg = cfg.get('visibility', {})

    # Apply per-camera overrides if provided (backward-compatible)
    overrides = cfg.get('visibility_overrides', {}).get(cam_name, {})
    eff_cfg = {**vis_cfg, **overrides}

    # Optional parameter validation warning
    try:
        import warnings
        if eff_cfg.get('tau_base_m', 0.0) > 0.6:
            warnings.warn(
                f"tau_base_m={eff_cfg.get('tau_base_m')} exceeds recommended maximum (0.6) for camera {cam_name}"
            )
    except Exception:
        pass

    # Sphere-only path
    r, st = visibility_via_sphere_projection(
        box3d,
        cam,
        depth_img,
        sphere_radius_m=eff_cfg.get('sphere_radius_m', 0.15),
        tau_base_m=eff_cfg.get('tau_base_m', 0.3),
        tau_scale_per_m=eff_cfg.get('tau_scale_per_m', 0.02),
        occlusion_fraction_thr=eff_cfg.get('occlusion_fraction_thr', 0.2),
        min_neighbors=eff_cfg.get('min_neighbors', 3),
        treat_no_depth_as_visible=eff_cfg.get('treat_no_depth_as_visible', True),
        max_window_px=eff_cfg.get('max_window_px', 15),
    )

    # Diagnostics are handled inside sphere_projection when enabled via cfg.
    return r, st

