import numpy as np
from typing import Dict, List, Any


def calculate_camera_consistency_metrics(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate multi-camera consistency metrics.

    Expected item structure in `results` per entry:
    {
        'camera': str,
        'visible': bool,                 # final gating + data availability
        'visibility': float,             # per-camera visibility ratio (0..1), if available
        'fov_fallback_used': bool,       # optional, whether corner fallback was used
    }
    """
    n = len(results) if results else 0
    if n == 0:
        return {
            'camera_coverage_ratio': 0.0,
            'visibility_variance_across_cameras': 0.0,
            'fov_recovery_rate': 0.0,
            'per_camera_metrics': {},
        }

    visible_items = [r for r in results if r.get('visible')]
    camera_coverage_ratio = len(visible_items) / n if n > 0 else 0.0

    visible_vis_values = [float(r.get('visibility', 0.0)) for r in visible_items if r.get('visibility') is not None]
    visibility_variance = float(np.var(visible_vis_values)) if len(visible_vis_values) > 1 else 0.0

    fov_fallback_used = [r for r in results if r.get('fov_fallback_used', False)]
    fov_recovery_rate = len(fov_fallback_used) / n if n > 0 else 0.0

    # Per-camera aggregation
    per_cam: Dict[str, Dict[str, float]] = {}
    cameras = sorted(set([r.get('camera') for r in results if r.get('camera') is not None]))
    for cam in cameras:
        items_cam = [r for r in results if r.get('camera') == cam]
        vis_cam = [r for r in items_cam if r.get('visible')]
        coverage = len(vis_cam) / len(items_cam) if items_cam else 0.0
        avg_visibility = float(np.mean([float(r.get('visibility', 0.0)) for r in vis_cam])) if vis_cam else 0.0
        per_cam[cam] = {
            'coverage': coverage,
            'avg_visibility': avg_visibility,
        }

    return {
        'camera_coverage_ratio': float(camera_coverage_ratio),
        'visibility_variance_across_cameras': float(visibility_variance),
        'fov_recovery_rate': float(fov_recovery_rate),
        'per_camera_metrics': per_cam,
    }


def benchmark_performance(before: Dict[str, Any], after: Dict[str, Any]) -> Dict[str, Any]:
    """
    Compare performance/metrics snapshots captured before and after optimization.

    Each snapshot dict is expected to include at least:
    {
        'camera_coverage_ratio': float,
        'fov_recovery_rate': float,
        'processing_time': float,  # ms/object or similar consistent unit
    }

    Returns summary including improvement percentages; assertions should be handled by caller.
    """
    def pct(delta, base):
        if base == 0:
            return 0.0
        return 100.0 * (delta / base)

    coverage_impr = (after.get('camera_coverage_ratio', 0.0) - before.get('camera_coverage_ratio', 0.0))
    fallback_impr = (after.get('fov_recovery_rate', 0.0) - before.get('fov_recovery_rate', 0.0))
    t_before = before.get('processing_time', 0.0)
    t_after = after.get('processing_time', 0.0)

    time_increase_pct = pct((t_after - t_before), t_before) if t_before > 0 else 0.0

    return {
        'improvement': {
            'camera_coverage_pct_points': float(coverage_impr * 100.0),
            'fov_recovery_pct_points': float(fallback_impr * 100.0),
        },
        'performance': {
            'time_increase_pct': float(time_increase_pct),
        }
    }

